import { describe, it, expect } from 'vitest';
import { validateEmail, isInternalEmail } from '../utils/validation';

describe('Email Validation', () => {
  describe('validateEmail', () => {
    it('validates correct email format', () => {
      expect(validateEmail('<EMAIL>')).toEqual({ isValid: true });
      expect(validateEmail('<EMAIL>')).toEqual({ isValid: true });
      expect(validateEmail('<EMAIL>')).toEqual({ isValid: true });
    });

    it('rejects invalid email formats', () => {
      expect(validateEmail('invalid-email')).toEqual({
        isValid: false,
        error: 'Please enter a valid email address'
      });
      expect(validateEmail('user@')).toEqual({
        isValid: false,
        error: 'Please enter a valid email address'
      });
      expect(validateEmail('@domain.com')).toEqual({
        isValid: false,
        error: 'Please enter a valid email address'
      });
    });

    it('rejects empty email', () => {
      expect(validateEmail('')).toEqual({
        isValid: false,
        error: 'Email is required'
      });
      expect(validateEmail('   ')).toEqual({
        isValid: false,
        error: 'Email is required'
      });
    });
  });

  describe('isInternalEmail', () => {
    it('identifies internal emails correctly', () => {
      expect(isInternalEmail('<EMAIL>')).toBe(true);
      expect(isInternalEmail('<EMAIL>')).toBe(true);
      expect(isInternalEmail('<EMAIL>')).toBe(true);
    });

    it('identifies external emails correctly', () => {
      expect(isInternalEmail('<EMAIL>')).toBe(false);
      expect(isInternalEmail('<EMAIL>')).toBe(false);
      expect(isInternalEmail('<EMAIL>')).toBe(false);
    });
  });
});
