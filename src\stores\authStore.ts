import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { AuthState, AuthUser, Permission } from '../types/auth';

interface AuthStore extends AuthState {
  setUser: (user: AuthUser | null) => void;
  setPermissions: (permissions: Permission[]) => void;
  getUserFunctions: () => string[],
  isAdmin: () => boolean;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setLinkSent: (email: string) => void;
  clearLinkSent: () => void;
  reset: () => void;
}

const initialState: AuthState = {
  user: null,
  permissions: [],
  isLoading: true,
  isAuthenticated: false,
  hasReachteamAccess: false,
  error: null,
  linkSent: false,
  linkSentEmail: null,
};

export const useAuthStore = create<AuthStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      setUser: (user) => set((state) => ({
        ...state,
        user,
        isAuthenticated: !!user,
      }), false, 'setUser'),
      
      setPermissions: (permissions) => set((state) => ({
        ...state,
        permissions,
        hasReachteamAccess: permissions.some(p => p.app === 'REACHTEAM'),
      }), false, 'setPermissions'),

      getUserFunctions : () => {
        const { permissions } = get();
        const app = permissions.find(p => p.app === 'REACHTEAM');
        const result = app?.roles?.flatMap(r => r.functions).map(r => r.function) ?? [];
        console.log('User Functions:', result);
        return app?.roles?.flatMap(r => r.functions).map(r => r.function) ?? [];
      },

      isAdmin: () => {
        const { permissions } = get();
        const app = permissions.find(p => p.app === 'REACHTEAM');
        const rolesArrTxt = app?.roles.map(r => r.role);
        return ['*', 'ADMIN'].some((r) => rolesArrTxt?.includes(r)) 
      },

      setLoading: (isLoading) => set((state) => ({
        ...state,
        isLoading,
      }), false, 'setLoading'),
      
      setError: (error) => set((state) => ({
        ...state,
        error,
        isLoading: false,
      }), false, 'setError'),

      setLinkSent: (email) => set((state) => ({
        ...state,
        linkSent: true,
        linkSentEmail: email,
        isLoading: false,
        error: null,
      }), false, 'setLinkSent'),

      clearLinkSent: () => set((state) => ({
        ...state,
        linkSent: false,
        linkSentEmail: null,
      }), false, 'clearLinkSent'),
      
      reset: () => set(initialState, false, 'reset'),
    }),
    {
      name: 'auth-store',
    }
  )
);
