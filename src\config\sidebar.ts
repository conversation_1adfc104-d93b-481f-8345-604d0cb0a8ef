import { mdiAccountClockOutline, mdiAccountMultipleCheckOutline, mdiAccountMultiplePlusOutline, mdiCalendarAccountOutline, mdiMapMarkerAccountOutline } from "@mdi/js";
import type { SidebarItem } from "../types/navigation";

export const SIDEBAR_CONFIG: SidebarItem[] = [

    {
        function: 'ROUNDS',
        label: 'Distribution',
        children: [
            {
                label: 'Rounds',
                path: `distribution/rounds`,
                icon: mdiCalendarAccountOutline
            },
             {
                label: 'Map',
                path: `distribution/map`,
                icon: mdiMapMarkerAccountOutline
            },
        ]
    },
    {
        function: 'WALKERS',
        label: 'Walkers',
        children: [
            {
                label: 'Activated',
                path: `walkers/activated`,
                icon: mdiAccountMultipleCheckOutline,
            },
            {
                label: 'Prospect',
                path: `walkers/prospect`,
                icon: mdiAccountMultiplePlusOutline,
            },
            {
                label: 'Historical',
                path: `walkers/historical`,
                icon: mdiAccountClockOutline

            }
        ]
    },
    // Add more functions as needed
];