import React from 'react';

export const DistributionMap: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">
          Distribution Map
        </h2>
        <div className="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
          <div className="text-center">
            <p className="text-gray-600">
              Interactive distribution map will be displayed here.
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Content coming soon...
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DistributionMap;