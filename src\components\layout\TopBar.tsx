import React from 'react';
import { mdiMenu } from '@mdi/js';
import Icon from '@mdi/react';
import { useLocation } from 'react-router-dom';

interface TopBarProps {
  onMenuClick: () => void;
}

const getPageTitle = (pathname: string): string => {
  const routes: Record<string, string> = {
    '/distribution/rounds': 'Distribution Rounds',
    '/distribution/map': 'Distribution Map',
  };
  return routes[pathname] || 'ReachTeam';
};

const TopBar: React.FC<TopBarProps> = ({ onMenuClick }) => {
  const location = useLocation();
  const pageTitle = getPageTitle(location.pathname);

  return (
    <header className="bg-white shadow-sm h-16 flex items-center px-4 fixed w-full top-0 z-50">
      <button
        onClick={onMenuClick}
        className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
        aria-label="Toggle navigation"
      >
        <Icon path={mdiMenu} size={1} />
      </button>
      <h1 className="ml-4 text-xl font-semibold text-gray-800">{pageTitle}</h1>
    </header>
  );
};

export default TopBar;