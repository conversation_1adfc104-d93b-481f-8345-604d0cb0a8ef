import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';
import { QueryProvider } from './providers/QueryProvider';
import { ProtectedRoute } from './components/ProtectedRoute';
import Login from './pages/Login';
import Unauthorized from './pages/Unauthorized';
import Error from './pages/Error';
import DistributionRounds from './pages/DistributionRounds';
import './App.css';
import { MainLayout } from './components/layout/MainLayout';
import DistributionMap from './pages/DistributionMap';

const ErrorFallback: React.FC<{ error: Error; resetErrorBoundary: () => void }> = ({ 
  error, 
  resetErrorBoundary 
}) => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="text-center">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">Something went wrong</h2>
      <p className="text-gray-600 mb-4">{error.message}</p>
      <button
        onClick={resetErrorBoundary}
        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
      >
        Try again
      </button>
    </div>
  </div>
);

function App() {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <QueryProvider>
        <Router>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/unauthorized" element={<Unauthorized />} />
            <Route path="/error" element={<Error />} />
            
            <Route  path="/"
              element={
                <ProtectedRoute>
                  <MainLayout>
                  </MainLayout>
                </ProtectedRoute>
              }
            >
              <Route path="distribution/rounds" element={<DistributionRounds />} />
              <Route path="distribution/map" element={<DistributionMap />} />
              <Route index element={<Navigate to="/distribution/rounds" replace />} />
            </Route>
            
           
            <Route path="*" element={<Navigate to="/distribution/rounds" replace />} />
            
          </Routes>
        </Router>
      </QueryProvider>
    </ErrorBoundary>
  );
}
// function App() {
//   return (
//     <ErrorBoundary FallbackComponent={ErrorFallback}>
//       <QueryProvider>
//         <Router>
//           <Routes>
//             <Route path="/login" element={<Login />} />
//             <Route path="/unauthorized" element={<Unauthorized />} />
//             <Route path="/error" element={<Error />} />
            
//             <Route
//               path="/distribution/rounds"
//               element={
//                 <ProtectedRoute>
//                   <DistributionRounds />
//                 </ProtectedRoute>
//               }
//             />
            
//             <Route path="/" element={<Navigate to="/distribution/rounds" replace />} />
//             <Route path="*" element={<Navigate to="/distribution/rounds" replace />} />
//           </Routes>
//         </Router>
//       </QueryProvider>
//     </ErrorBoundary>
//   );
// }

export default App;

// import React from 'react';
// import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
// import { ErrorBoundary } from 'react-error-boundary';
// import { QueryProvider } from './providers/QueryProvider';
// import { ProtectedRoute } from './components/ProtectedRoute';
// import { MainLayout } from './components/Layout/MainLayout';
// import Login from './pages/Login';
// import Unauthorized from './pages/Unauthorized';
// import Error from './pages/Error';
// import DistributionRounds from './pages/DistributionRounds';
// ...existing imports...

