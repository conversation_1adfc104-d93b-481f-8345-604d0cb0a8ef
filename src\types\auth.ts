export interface AuthUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
}

export interface CustomClaims {
  app?: string;
  [key: string]: unknown;
}

export interface AuthToken {
  token: string;
  claims: CustomClaims;
}

export interface Permission {
  app: string;
  customer?: {
    cust_no: string;
    is_newspaper: boolean;
    name: string;
  };
  pers_code?: string;
  roles: Role[];
}

export interface Role {
  role: string;
  functions: RoleFunction[];
}

export interface RoleFunction {
  function: string;
}

export interface AuthState {
  user: AuthUser | null;
  permissions: Permission[];
  isLoading: boolean;
  isAuthenticated: boolean;
  hasReachteamAccess: boolean;
  error: string | null;
  linkSent: boolean;
  linkSentEmail: string | null;
}

export const AuthStatus = {
  LOADING: 'loading',
  AUTHENTICATED: 'authenticated',
  UNAUTHENTICATED: 'unauthenticated',
  UNAUTHORIZED: 'unauthorized',
  ERROR: 'error'
} as const;

export interface AuthError extends Error {
  code: string;
  message: string;
}
