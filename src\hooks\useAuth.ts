import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { httpsCallable } from 'firebase/functions';
import { 
  signInWithEmailLink,
  isSignInWithEmailLink,
  signInWithPopup,
  GoogleAuthProvider,
  signOut, 
  onAuthStateChanged,
  type User 
} from 'firebase/auth';
import { auth, functions } from '../config/firebase';
import { useAuthStore } from '../stores/authStore';
import type { Permission, AuthUser, CustomClaims } from '../types/auth';
import { userGateway } from '../const/endppints';
import { isInternalEmail } from '../utils/validation';

const addCustomClaim = httpsCallable(functions, 'addCustomClaim');
const sendAuthLinkEmail = httpsCallable(functions, 'sendAuthLinkEmail');

const CUSTOM_OAUTH_PARAMETERS = {
  hd: 'reach.nz',
  prompt: 'select_account'
};

interface PermissionsResponse {
  apps: Permission[];
}

const fetchPermissions = async (token: string): Promise<Permission[]> => {
  const response = await fetch(`${userGateway}/permissions`, {
    headers: {
      'Firebase-Token': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch permissions: ${response.statusText}`);
  }

  const result: PermissionsResponse = await response.json();
  return result.apps || [];
};

const isReachteamUser = (user: User): boolean => {
  const email = user.email;
  const providerData = user.providerData[0];
  
  return (
    (email?.endsWith('@reach.nz') ?? false) ||
    providerData?.providerId === 'password'
  );
};

const hasRequiredCustomClaim = (claims: CustomClaims): boolean => {
  return claims.app === 'REACHTEAM';
};

export const useAuth = () => {
  const queryClient = useQueryClient();
  const { setUser, setPermissions, setLoading, setError, setLinkSent, clearLinkSent, reset } = useAuthStore();

  const authQuery = useQuery({
    queryKey: ['auth'],
    queryFn: () => new Promise<AuthUser | null>((resolve) => {
      const unsubscribe = onAuthStateChanged(auth, (user) => {
        unsubscribe();
        if (user) {
          resolve({
            uid: user.uid,
            email: user.email,
            displayName: user.displayName,
            photoURL: user.photoURL,
            emailVerified: user.emailVerified,
          });
        } else {
          resolve(null);
        }
      });
    }),
    staleTime: Infinity,
  });

  const permissionsQuery = useQuery({
    queryKey: ['permissions', authQuery.data?.uid],
    queryFn: async () => {
      if (!authQuery.data) return [];
      
      const user = auth.currentUser;
      if (!user) throw new Error('No authenticated user');

      // Check if user is reachteam user
      if (!isReachteamUser(user)) {
        await signOut(auth);
        throw new Error('AUTHORIZATION_ERROR: User is not authorized for this application');
      }

      // Get ID token and check claims
      const idTokenResult = await user.getIdTokenResult();
      const claims = idTokenResult.claims as CustomClaims;

      // Add custom claim if not present
      if (!hasRequiredCustomClaim(claims)) {
        await addCustomClaim();
        await user.getIdToken(true);
      }

      // Fetch permissions - any error here is a fetch error, not auth error
      const token = await user.getIdToken();
      return fetchPermissions(token);
    },
    enabled: !!authQuery.data,
    retry: (failureCount, error) => {
      // Don't retry authorization errors
      if (error?.message?.includes('AUTHORIZATION_ERROR')) {
        return false;
      }
      return failureCount < 3;
    },
  });

  const loginMutation = useMutation({
    mutationFn: async ({ email }: { email: string }) => {
      if (isInternalEmail(email)) {
        // For internal emails, use Google sign-in with popup
        const provider = new GoogleAuthProvider();
        provider.setCustomParameters(CUSTOM_OAUTH_PARAMETERS);
        
        const userCredential = await signInWithPopup(auth, provider);
        return { type: 'internal', email, user: userCredential.user };
      } else {
        // For external emails, use GCP function
        const redirectUrl = window.location.href;
        await sendAuthLinkEmail({ auth_email: email, redirectUrl });
        return { type: 'external', email };
      }
    },
    onSuccess: (result) => {
      if (result.type === 'internal') {
        // For internal users, authentication is complete
        queryClient.invalidateQueries({ queryKey: ['auth'] });
      } else {
        // For external users, show link sent screen
        setLinkSent(result.email);
      }
    },
    onError: (error) => {
      setError(error.message);
    },
  });

  const completeEmailLinkSignIn = useMutation({
    mutationFn: async ({ email, url }: { email: string; url: string }) => {
      const userCredential = await signInWithEmailLink(auth, email, url);
      window.localStorage.removeItem('emailForSignIn');
      return userCredential.user;
    },
    onSuccess: () => {
      clearLinkSent();
      queryClient.invalidateQueries({ queryKey: ['auth'] });
    },
    onError: (error) => {
      setError(error.message);
    },
  });

  const logoutMutation = useMutation({
    mutationFn: async () => {
      await signOut(auth);
    },
    onSuccess: () => {
      reset();
      queryClient.clear();
    },
  });

  // Check for email link authentication on component mount
  React.useEffect(() => {
    const url = window.location.href;
    if (isSignInWithEmailLink(auth, url)) {
      let email = window.localStorage.getItem('emailForSignIn');
      if (!email) {
        email = window.prompt('Please provide your email for confirmation');
      }
      if (email) {
        completeEmailLinkSignIn.mutate({ email, url });
      }
    }
  }, []);

  // Update store when queries change
  React.useEffect(() => {
    setUser(authQuery.data || null);
    setLoading(authQuery.isLoading || permissionsQuery.isLoading);
  }, [authQuery.data, authQuery.isLoading, permissionsQuery.isLoading, setUser, setLoading]);

  React.useEffect(() => {
    if (permissionsQuery.data) {
      setPermissions(permissionsQuery.data);
    }
  }, [permissionsQuery.data, setPermissions]);

  React.useEffect(() => {
    const error = authQuery.error || permissionsQuery.error;
    setError(error ? error.message : null);
  }, [authQuery.error, permissionsQuery.error, setError]);

  return {
    user: authQuery.data,
    permissions: permissionsQuery.data || [],
    isLoading: authQuery.isLoading || permissionsQuery.isLoading,
    isAuthenticated: !!authQuery.data,
    hasReachteamAccess: permissionsQuery.data?.some(p => p.app === 'REACHTEAM') ?? false,
    error: authQuery.error || (permissionsQuery.error?.message?.includes('AUTHORIZATION_ERROR') ? null : permissionsQuery.error),
    login: loginMutation.mutate,
    logout: logoutMutation.mutate,
    completeEmailLinkSignIn: completeEmailLinkSignIn.mutate,
    isLoginLoading: loginMutation.isPending,
    isLogoutLoading: logoutMutation.isPending,
    isCompletingEmailLink: completeEmailLinkSignIn.isPending,
  };
};


