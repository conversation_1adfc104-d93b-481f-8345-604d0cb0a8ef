import React from 'react';
import { useAuth } from '../hooks/useAuth';

export const Unauthorized: React.FC = () => {
  const { logout, user } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <div className="mx-auto h-12 w-12 text-red-600">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Access Denied
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            You don't have permission to access ReachTeam.
          </p>
          {user?.email && (
            <p className="mt-1 text-xs text-gray-500">
              Signed in as: {user.email}
            </p>
          )}
        </div>
        
        <div className="mt-8 space-y-4">
          <div className="rounded-md bg-yellow-50 p-4">
            <div className="text-sm text-yellow-800">
              <p className="font-medium">Access Requirements:</p>
              <ul className="mt-2 list-disc list-inside space-y-1">
                <li>Must have a @reach.nz email address, or</li>
                <li>Must sign in with password authentication</li>
                <li>Must have ReachTeam permissions assigned</li>
              </ul>
            </div>
          </div>
          
          <p className="text-sm text-gray-600">
            If you believe this is an error, please contact your administrator.
          </p>
          
          <button
            onClick={handleLogout}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Sign Out
          </button>
        </div>
      </div>
    </div>
  );
};

export default Unauthorized;
