import { http, HttpResponse } from 'msw';
import { userGateway } from '../../const/endppints';
import type { Permission } from '../../types/auth';

const mockPermissions: Permission[] = [
  {
    app: 'REACHTEAM',
    pers_code: 'DS308078',
    roles: [
      {
        role: '*',
        functions: [],
      },
      {
        role: 'ADMIN',
        functions: [],
      },
    ],
  },
];

export const handlers = [
  // Mock permissions endpoint
  http.get(`${userGateway}/permissions`, ({ request }) => {
    const authorization = request.headers.get('authorization');
    
    if (!authorization || !authorization.startsWith('Bearer ')) {
      return new HttpResponse(null, { status: 401 });
    }

    return HttpResponse.json({ data: mockPermissions });
  }),

  // Mock permissions endpoint - unauthorized user
  http.get(`${userGateway}/permissions`, ({ request }) => {
    const authorization = request.headers.get('authorization');
    
    if (authorization?.includes('unauthorized')) {
      return HttpResponse.json({ data: [] });
    }

    if (!authorization || !authorization.startsWith('Bearer ')) {
      return new HttpResponse(null, { status: 401 });
    }

    return HttpResponse.json({ data: mockPermissions });
  }),
];
