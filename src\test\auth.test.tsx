import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Login from '../pages/Login';

// Mock Firebase
vi.mock('../config/firebase', () => ({
  auth: {
    currentUser: null,
    config: { emulator: null },
  },
  functions: {
    config: { emulator: null },
  },
}));

// Mock the auth store
vi.mock('../stores/authStore', () => ({
  useAuthStore: vi.fn(() => ({
    user: null,
    permissions: [],
    isLoading: false,
    isAuthenticated: false,
    hasReachteamAccess: false,
    error: null,
    linkSent: false,
    linkSentEmail: null,
    setUser: vi.fn(),
    setPermissions: vi.fn(),
    setLoading: vi.fn(),
    setError: vi.fn(),
    setLinkSent: vi.fn(),
    clearLinkSent: vi.fn(),
    reset: vi.fn(),
  })),
}));

// Mock Firebase Auth
vi.mock('firebase/auth', () => ({
  signInWithEmailLink: vi.fn(),
  isSignInWithEmailLink: vi.fn(() => false),
  sendSignInLinkToEmail: vi.fn(),
  signOut: vi.fn(),
  onAuthStateChanged: vi.fn((_auth, callback) => {
    callback(null);
    return () => {};
  }),
}));

// Mock Firebase Functions
vi.mock('firebase/functions', () => ({
  httpsCallable: vi.fn(() => vi.fn()),
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Authentication Flow', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Login Component', () => {
    it('renders login form elements', () => {
      render(<Login />, { wrapper: createWrapper() });
      
      expect(screen.getByRole('heading', { name: /sign in to reachteam/i })).toBeInTheDocument();
      expect(screen.getByText(/internal users: sign in with google/i)).toBeInTheDocument();
    });
  });
});
