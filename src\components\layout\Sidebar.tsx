import React from 'react';
import { NavLink } from 'react-router-dom';
import { mdiMapOutline, mdiViewDashboardOutline } from '@mdi/js';
import Icon from '@mdi/react';
import { useAuthStore } from '../../stores/authStore';
import { SIDEBAR_CONFIG } from '../../config/sidebar';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

interface MenuItem {
  title: string;
  icon: string;
  path: string;
}

interface MenuSection {
  title: string;
  items: MenuItem[];
}

const menuSections: MenuSection[] = [
  {
    title: 'Distribution',
    items: [
      {
        title: 'Rounds',
        icon: mdiViewDashboardOutline,
        path: '/distribution/rounds',
      },
      {
        title: 'Map',
        icon: mdiMapOutline,
        path: '/distribution/map',
      },
    ],
  },
];

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const getUserFunctions = useAuthStore(state => state.getUserFunctions);
  const userFunctions = getUserFunctions();
  const isAdmin = useAuthStore(state => state.isAdmin);
  const userSidebarItems = SIDEBAR_CONFIG.filter(item => isAdmin() || userFunctions.includes(item.function));
  
  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`
          fixed top-16 left-0 h-[calc(100vh-4rem)] w-64 bg-white shadow-lg
          transform transition-transform duration-300 ease-in-out z-50
          ${isOpen ? 'translate-x-0' : '-translate-x-full'}
          lg:translate-x-0 lg:relative lg:top-0
        `}
      >
        <nav className="h-full overflow-y-auto py-4">
          {userSidebarItems.map((item) => (
           
          ))}
          {/* {menuSections.map((section) => (
            <div key={section.title} className="px-4 mb-4">
              <h2 className="text-xs font-semibold text-gray-600 uppercase tracking-wider mb-2">
                {section.title}
              </h2>
              <ul>
                {section.items.map((item) => (
                  <li key={item.path}>
                    <NavLink
                      to={item.path}
                      className={({ isActive }) =>
                        `flex items-center px-4 py-2 text-gray-700 rounded-lg
                        ${isActive ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-100'}
                        transition-colors duration-200`
                      }
                    >
                      <Icon path={item.icon} size={1} className="mr-3" />
                      <span>{item.title}</span>
                    </NavLink>
                  </li>
                ))}
              </ul>
            </div>
          ))} */}
        </nav>
      </aside>
    </>
  );
};

export default Sidebar;