import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFunctions } from 'firebase/functions';

// const firebaseConfig = {
//   apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
//   authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
//   projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
//   storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
//   messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
//   appId: import.meta.env.VITE_FIREBASE_APP_ID,
// };
console.log('Firebase config:', import.meta.env.VITE_FIREBASE_CONFIG);
const app = initializeApp(JSON.parse(import.meta.env.VITE_FIREBASE_CONFIG));

export const auth = getAuth(app);
export const functions = getFunctions(app, 'australia-southeast1');

// Connect to emulators in development
// if (import.meta.env.DEV) {
//   const authEmulatorHost = import.meta.env.VITE_FIREBASE_AUTH_EMULATOR_HOST;
//   const functionsEmulatorHost = import.meta.env.VITE_FIREBASE_FUNCTIONS_EMULATOR_HOST;
  
//   if (authEmulatorHost) {
//     try {
//       connectAuthEmulator(auth, `http://${authEmulatorHost}`);
//     } catch (error) {
//       // Emulator already connected
//     }
//   }
  
//   if (functionsEmulatorHost) {
//     try {
//       connectFunctionsEmulator(functions, 'localhost', 5001);
//     } catch (error) {
//       // Emulator already connected
//     }
//   }
// }

export default app;
