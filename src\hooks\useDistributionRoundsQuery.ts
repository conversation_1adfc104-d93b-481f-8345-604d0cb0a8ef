import { useSearchParams } from 'react-router-dom';
import { useCallback, useMemo } from 'react';
import type { DistributionRoundsQuery } from '../types/distributionRounds';
import { DEFAULT_QUERY, VALID_SORT_ORDERS } from '../types/distributionRounds';

const validateViewOffset = (value: string): number => {
  const parsed = parseInt(value, 10);
  return isNaN(parsed) || parsed < 0 ? DEFAULT_QUERY.viewoffset : parsed;
};

const validateSortOrder = (value: string): 'Ascending' | 'Descending' => {
  return VALID_SORT_ORDERS.includes(value as any) 
    ? (value as 'Ascending' | 'Descending')
    : DEFAULT_QUERY.sortOrder;
};

const validateStringParam = (value: string | null, defaultValue: string): string => {
  return value ?? defaultValue;
};

export const useDistributionRoundsQuery = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  const query = useMemo((): DistributionRoundsQuery => {
    return {
      viewoffset: validateViewOffset(searchParams.get('viewoffset') ?? ''),
      searchTxt: validateStringParam(searchParams.get('searchTxt'), DEFAULT_QUERY.searchTxt),
      sortParam: validateStringParam(searchParams.get('sortParam'), DEFAULT_QUERY.sortParam),
      sortOrder: validateSortOrder(searchParams.get('sortOrder') ?? ''),
      round: validateStringParam(searchParams.get('round'), DEFAULT_QUERY.round),
      address: validateStringParam(searchParams.get('address'), DEFAULT_QUERY.address),
    };
  }, [searchParams]);

  const updateQuery = useCallback((updates: Partial<DistributionRoundsQuery>) => {
    const newParams = new URLSearchParams(searchParams);
    
    Object.entries(updates).forEach(([key, value]) => {
      if (value === '' || value === DEFAULT_QUERY[key as keyof DistributionRoundsQuery]) {
        newParams.delete(key);
      } else {
        newParams.set(key, String(value));
      }
    });

    setSearchParams(newParams, { replace: true });
  }, [searchParams, setSearchParams]);

  const resetQuery = useCallback(() => {
    setSearchParams({}, { replace: true });
  }, [setSearchParams]);

  const setDefaults = useCallback(() => {
    const params = new URLSearchParams();
    Object.entries(DEFAULT_QUERY).forEach(([key, value]) => {
      params.set(key, String(value));
    });
    setSearchParams(params, { replace: true });
  }, [setSearchParams]);

  return {
    query,
    updateQuery,
    resetQuery,
    setDefaults,
  };
};