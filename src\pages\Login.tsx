import React, { useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useAuthStore } from '../stores/authStore';
import { validateEmail } from '../utils/validation';

const LinkSentScreen: React.FC<{ email: string; onBack: () => void }> = ({ email, onBack }) => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div className="max-w-md w-full space-y-8 text-center">
      <div>
        <div className="mx-auto h-12 w-12 text-green-600">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 7.89a2 2 0 002.83 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
          Check your email
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          We've sent a sign-in link to
        </p>
        <p className="mt-1 text-sm font-medium text-gray-900">
          {email}
        </p>
      </div>
      
      <div className="mt-8 space-y-4">
        <div className="rounded-md bg-blue-50 p-4">
          <div className="text-sm text-blue-800">
            <p className="font-medium">Next steps:</p>
            <ul className="mt-2 list-disc list-inside space-y-1">
              <li>Check your email inbox</li>
              <li>Click the sign-in link in the email</li>
              <li>You'll be automatically signed in</li>
            </ul>
          </div>
        </div>
        
        <p className="text-xs text-gray-500">
          Didn't receive the email? Check your spam folder or try again.
        </p>
        
        <button
          onClick={onBack}
          className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Try with different email
        </button>
      </div>
    </div>
  </div>
);

export const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState<string | null>(null);
  const { isAuthenticated, login, isLoginLoading, error } = useAuth();
  const { linkSent, linkSentEmail, clearLinkSent } = useAuthStore();
  const location = useLocation();

  const from = location.state?.from?.pathname || '/distribution/rounds';

  if (isAuthenticated) {
    return <Navigate to={from} replace />;
  }

  if (linkSent && linkSentEmail) {
    return <LinkSentScreen email={linkSentEmail} onBack={clearLinkSent} />;
  }

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);
    
    if (emailError && value.trim()) {
      const validation = validateEmail(value);
      if (validation.isValid) {
        setEmailError(null);
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validateEmail(email);
    if (!validation.isValid) {
      setEmailError(validation.error || 'Invalid email');
      return;
    }
    
    setEmailError(null);
    login({ email });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to ReachTeam
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
             Internal users: Sign in with Google • External users: Receive email link
          </p>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className={`appearance-none relative block w-full px-3 py-2 border placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm ${
                  emailError ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter your email address"
                value={email}
                onChange={handleEmailChange}
                disabled={isLoginLoading}
              />
              {emailError && (
                <p className="mt-1 text-sm text-red-600">{emailError}</p>
              )}
            </div>
          </div>

          {(error && !emailError) && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="text-sm text-red-800">
                {error.message || 'An error occurred while sending the sign-in link'}
              </div>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={isLoginLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoginLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin -ml-1 mr-3 h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
                  Signing in...
                </div>
              ) : (
                'Sign in'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Login;
