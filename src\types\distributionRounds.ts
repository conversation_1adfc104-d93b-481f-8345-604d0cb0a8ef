export interface DistributionRoundsQuery {
  viewoffset: number;
  searchTxt: string;
  sortParam: string;
  sortOrder: 'Ascending' | 'Descending';
  round: string;
  address: string;
}

export const DEFAULT_QUERY: DistributionRoundsQuery = {
  viewoffset: 0,
  searchTxt: '',
  sortParam: 'walk_name',
  sortOrder: 'Ascending',
  round: '',
  address: '',
};

export const VALID_SORT_ORDERS = ['Ascending', 'Descending'] as const;