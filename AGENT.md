# ReachTeam React App - Agent Guidelines

## Project Overview
A React application for ReachTeam with Firebase authentication and role-based access control.

## Commands
- **Development**: `npm run dev`
- **Build**: `npm run build`
- **Lint**: `npm run lint`
- **Test**: `npm run test`
- **Test UI**: `npm run test:ui`
- **Test Coverage**: `npm run test:coverage`
- **Typecheck**: `npx tsc --noEmit`

## Architecture

### Folder Structure
```
src/
├── components/          # Reusable UI components
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── stores/             # Zustand stores
├── types/              # TypeScript type definitions
├── config/             # Configuration files
├── providers/          # React context providers
├── test/               # Test files and utilities
└── const/              # Constants and endpoints
```

### Technology Stack
- **Frontend**: React 19 + TypeScript + Vite
- **Styling**: Tailwind CSS
- **Routing**: React Router v6
- **State Management**: Zustand + React Query
- **Authentication**: Firebase Auth
- **Testing**: Vitest + Testing Library + MSW
- **Error Handling**: React Error Boundary

### Authentication Flow
1. User enters email on login page with validation
2. If internal email (@reach.nz): Firebase sends magic link via sendSignInLinkToEmail
3. If external email: GCP function 'sendAuthLinkEmail' sends custom magic link
4. User sees "check your email" screen with instructions
5. User clicks link and returns to login page for auto sign-in
6. Validate if user is reachteam user and ensure custom claim exists
7. Fetch user permissions from `/permissions` endpoint
8. Check if user has 'REACHTEAM' app permission
9. Route to appropriate page based on auth status

### Code Standards
- Use TypeScript strictly
- Functional components with hooks
- Error boundaries for error handling
- MSW for API mocking in tests
- Comprehensive test coverage for auth flow
- Production-grade error handling

### Environment Variables
Copy `.env.example` to `.env` and configure Firebase settings.

## Notes
- All routes except login/error/unauthorized require authentication
- Default authenticated route: `/distribution/rounds`
- Firebase functions region: `australia-southeast1`
- Custom claim function: `addCustomClaim`
